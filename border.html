<!doctype html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
<div class="bg-[#2d2e3e] text-white p-2 rounded border border-gray-500 w-48 shadow-lg">
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-cyan-400">console</span>
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-cyan-400">crossOriginIsolated</span>
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>

</div>
<hr />
<div class="relative w-56 p-2 bg-[#2d2e3e] text-white shadow-lg border border-gray-500 rounded-sm"
     style="clip-path: polygon(5px 0%, 100% 0%, 100% calc(100% - 5px), calc(100% - 5px) 100%, 0% 100%, 0% 5px);"
	 >
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>
  <div class="flex items-center justify-between text-sm hover:bg-[#3c3e50] px-2 py-1 rounded cursor-pointer">
    <span class="text-xs text-gray-300">(Lsp)</span>
  </div>
</div>
<button style="clip-path: polygon(5px 0%, 100% 0%, 100% calc(100% - 5px), calc(100% - 5px) 100%, 0% 100%, 0% 5px);" class="bg-black">
  demo
</button>


</body>
</html>