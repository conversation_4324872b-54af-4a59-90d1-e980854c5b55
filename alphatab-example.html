<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="utf-8" />
  <title>AlphaTab 示例</title>
  <script src="https://cdn.jsdelivr.net/npm/@coderline/alphatab@latest/dist/alphaTab.js"></script>
  <style type="text/css">
    .at-wrap {
      width: 80vw;
      height: 80vh;
      margin: 0 auto;
      border: 1px solid rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative;
    }
    .at-content {
      position: relative;
      overflow: hidden;
      flex: 1 1 auto;
    }
    .at-sidebar {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      max-width: 70px;
      width: auto;
      display: flex;
      align-content: stretch;
      z-index: 1001;
      overflow: hidden;
      border-right: 1px solid rgba(0, 0, 0, 0.12);
      background: #f7f7f7;
    }
    .at-viewport {
      overflow-y: auto;
      position: absolute;
      top: 0;
      left: 70px;
      right: 0;
      bottom: 0;
      padding-right: 20px;
    }
    .at-footer {
      flex: 0 0 auto;
      background: #436d9d;
      color: #fff;
      padding: 10px;
    }
    .at-controls {
      flex: 0 0 auto;
      display: flex;
      padding: 10px;
      background: #f7f7f7;
      border-top: 1px solid rgba(0, 0, 0, 0.12);
    }
    .at-controls button {
      margin-right: 10px;
      padding: 5px 10px;
      cursor: pointer;
    }
    .at-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.7);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1002;
    }
    .at-overlay-content {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
  </style>
</head>
<body>
  <div class="at-wrap">
    <!-- <div class="at-overlay">
      <div class="at-overlay-content">
        音乐谱表加载中...
      </div>
    </div> -->
    <div class="at-content">
      <div class="at-sidebar">
        <!-- 轨道选择器将在这里 -->
      </div>
      <div class="at-viewport">
        <div class="at-main"></div>
      </div>
    </div>
    <div class="at-controls">
      <button id="playPause">播放/暂停</button>
      <button id="stop">停止</button>
    </div>
    <div class="at-footer">
      使用 alphaTab 渲染
    </div>
  </div>

  <script type="text/javascript">
    // 获取DOM元素
    const wrapper = document.querySelector(".at-wrap");
    const main = wrapper.querySelector(".at-main");
    const overlay = wrapper.querySelector(".at-overlay");
    const playPauseBtn = document.getElementById("playPause");
    const stopBtn = document.getElementById("stop");

    // 初始化 alphaTab
    const settings = {
      // 使用 alphaTex 格式
      tex: true,
      // 播放器设置
      player: {
        enablePlayer: true,
        soundFont: "https://cdn.jsdelivr.net/npm/@coderline/alphatab@latest/dist/soundfont/sonivox.sf2"
      }
    };

    // 创建 alphaTab API 实例
    const api = new alphaTab.AlphaTabApi(main, settings);

    // 加载 alphaTex 格式的音乐谱表
    api.tex(`
    //   \title "简单示例"
    //   \subtitle "alphaTab 演示"
    //   \tempo 120
    //   \instrument 24
    //   \tuning e5 b4 g4 d4 a3 e3
      .

      :4 0.3 2.3 4.3 5.3 |
      :8 7.3 5.3 4.3 2.3 0.3 2.3 4.3 5.3 |
      :4 7.3 5.3 4.2 0.2 |
      :2 2.2 0.1 |
      :4 3.1{b (0 4)} 0.1 2.1 0.1 |
      :8 3.2 5.2 7.2 3.1 5.1 7.1 8.1 7.1 |
      :4 5.1 3.1 0.1 0.2 |
      :1 0.3{v}
    `);

    // 事件监听
    api.scoreLoaded.on(() => {
      // 谱表加载完成后隐藏加载中提示
      overlay.style.display = "none";
    });

    api.playerReady.on(() => {
      // 播放器准备就绪
      console.log("播放器已准备就绪");
    });

    api.playerStateChanged.on((e) => {
      // 播放器状态变化
      const state = e.state;
      console.log("播放器状态:", state);
    });

    // 按钮事件
    playPauseBtn.addEventListener("click", () => {
      if (api.playerState === alphaTab.synth.PlayerState.Playing) {
        api.pause();
      } else {
        api.play();
      }
    });

    stopBtn.addEventListener("click", () => {
      api.stop();
    });
  </script>
</body>
</html>