<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <script type="module">
    import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
    mermaid.registerIconPacks([
      {
        name: 'logos',
        loader: () =>
          fetch('https://unpkg.com/@iconify-json/logos@1/icons.json').then(res => res.json())
      }
    ]);
    mermaid.initialize({ startOnLoad: true, });
  </script>
</head>
<body>
  <div class="mermaid">
flowchart LR
  A(image:https://example.com/icon.svg)
  </div>
</body>
</html>
