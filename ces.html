<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Cesium 视频投射示例</title>
  <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>
  <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet" />
  <style>
    html, body, #cesiumContainer {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <div id="cesiumContainer"></div>

  <script>
    // 初始化 Cesium Viewer
    const viewer = new Cesium.Viewer("cesiumContainer", {
      terrainProvider: Cesium.createWorldTerrain()
    });

    // 创建视频元素
    const video = document.createElement("video");
    video.src = "https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.mp4";
    video.autoplay = true;
    video.loop = true;
    video.muted = true;
    video.crossOrigin = "anonymous";
    video.playsInline = true;
    video.play();

    // 创建视频材质
    const videoMaterial = new Cesium.ImageMaterialProperty({
      image: video,
      transparent: true
    });

    // 添加矩形图元并应用视频材质
    viewer.entities.add({
      name: "Video Rectangle",
      rectangle: {
        coordinates: Cesium.Rectangle.fromDegrees(-75.10, 39.57, -75.02, 39.62), // 在美国费城区域
        material: videoMaterial
      }
    });

    // 相机跳转过去
    viewer.camera.flyTo({
      destination: Cesium.Rectangle.fromDegrees(-75.10, 39.57, -75.02, 39.62)
    });
  </script>
</body>
</html>
