<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>3D Force Graph Simple Example</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      overflow: hidden;
      width: 100%;
      height: 100%;
    }
    #3d-graph {
      width: 100vw;
      height: 100vh;
    }
  </style>
</head>
<body>
  <div id="3d-graph"></div>
  <script src="https://unpkg.com/three@0.152.2/build/three.min.js"></script>
  <script src="https://unpkg.com/3d-force-graph"></script>
  <script>
    // 手动定义节点和连线
    const nodes = [
      { id: 'A', x: 0, y: 0, z: 0, fx: 0, fy: 0, fz: 0 },
      { id: 'B', x: 100, y: 0, z: 0, fx: 100, fy: 0, fz: 0 },
      { id: 'C', x: 0, y: 100, z: 0, fx: 0, fy: 100, fz: 0 }
    ];
    const links = [
      { source: 'A', target: 'B' },
      { source: 'A', target: 'C' }
    ];

    // 初始化节点大小
    nodes.forEach(n => n.__val = 6);

    const myGraph = ForceGraph3D()(document.getElementById('3d-graph'))
      .graphData({ nodes, links })
      .nodeLabel(node => node.id)
      .nodeAutoColorBy('id')
      .nodeRelSize(6)
      .enableNodeDrag(false)
      .cooldownTicks(0)
      .nodeVal(n => n.__val)
      .linkDirectionalParticles(2)
      .linkDirectionalParticleSpeed(0.01)
      .linkColor(() => 'yellow')
      .linkWidth(1) // 所有连线宽度为4
	  .linkOpacity(0.6);

    // 悬停放大
    let lastHoverNode = null;
    myGraph.onNodeHover(node => {
      if (lastHoverNode) lastHoverNode.__val = 6;
      if (node) node.__val = 18;
      lastHoverNode = node;
      myGraph.nodeVal(n => n.__val || 6);
    });
    // 点击弹窗
  </script>
</body>ipt>
</html>
</html>