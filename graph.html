<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>3D Force Graph Example</title>
	<style>
		html,
		body {
			margin: 0;
			padding: 0;
			overflow: hidden;
			width: 100%;
			height: 100%;
		}

		#3d-graph {
			width: 100%;
			height: 100%;
		}
	</style>
</head>

<body>
	<div id="3d-graph"></div>

	<!-- 引入依赖 -->
	<script src="https://unpkg.com/three@0.152.2/build/three.min.js"></script>
	<script src="https://unpkg.com/3d-force-graph"></script>

	<script>
		const NODE_COUNT = 100;
		const nodes = [];
		const links = [];

		const GROUP_COUNT = 5; // 分5组
		const NODES_PER_GROUP = Math.floor(NODE_COUNT / GROUP_COUNT);
		const GROUP_RADIUS = 3000; // 组间距离
		const CHILD_RADIUS = 400;  // 子节点围绕父节点的距离

		for (let g = 0; g < GROUP_COUNT; g++) {
			const parentId = `${g}_parent`;
			const color = '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
			// 每组一个球心
			const groupPhi = Math.acos(-1 + (2 * g) / GROUP_COUNT);
			const groupTheta = Math.sqrt(GROUP_COUNT * Math.PI) * groupPhi;
			const groupX = GROUP_RADIUS * Math.cos(groupTheta) * Math.sin(groupPhi);
			const groupY = GROUP_RADIUS * Math.sin(groupTheta) * Math.sin(groupPhi);
			const groupZ = GROUP_RADIUS * Math.cos(groupPhi);

			// 父节点
			nodes.push({
				id: parentId,
				color,
				group: g,
				x: groupX, y: groupY, z: groupZ,
				fx: groupX, fy: groupY, fz: groupZ
			});
			// 子节点围绕父节点均匀分布
			for (let i = 0; i < NODES_PER_GROUP - 1; i++) {
				const childId = `${g}_${i}`;
				const phi = Math.acos(-1 + (2 * i) / (NODES_PER_GROUP - 1));
				const theta = Math.sqrt((NODES_PER_GROUP - 1) * Math.PI) * phi;
				const x = groupX + CHILD_RADIUS * Math.cos(theta) * Math.sin(phi);
				const y = groupY + CHILD_RADIUS * Math.sin(theta) * Math.sin(phi);
				const z = groupZ + CHILD_RADIUS * Math.cos(phi);
				nodes.push({
					id: childId,
					color,
					group: g,
					x, y, z, fx: x, fy: y, fz: z
				});
				links.push({ source: parentId, target: childId });
			}
		}

		const myGraph = ForceGraph3D()(document.getElementById('3d-graph'))
			.graphData({ nodes, links })
			.nodeLabel(node => node.id)
			.nodeAutoColorBy('color')
			.nodeRelSize(6)
			.enableNodeDrag(false)
			.cooldownTicks(0);

		let lastHoverNode = null;
		myGraph.onNodeHover(node => {
			if (lastHoverNode) {
				lastHoverNode.__val = 6;
			}
			if (node) {
				node.__val = 18;
			}
			lastHoverNode = node;
			myGraph.nodeVal(n => n.__val || 6);
		});
		// 初始化所有节点大小
		nodes.forEach(n => n.__val = 6);
		myGraph.nodeVal(n => n.__val);

		// 添加点击事件
		myGraph.onNodeClick(node => {
			alert(`节点ID: ${node.id}`);
		});
	</script>
</body>

</html>