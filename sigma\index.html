<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Sigma.js Force Layout Random Example</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100vw;
      height: 100vh;
      overflow: hidden;
    }
    #sigma-container {
      width: 100vw;
      height: 100vh;
      background: #222;
    }
  </style>
  <script src="https://cdn.jsdelivr.net/npm/graphology@0.25.1/dist/graphology.umd.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/sigma@2.4.0/build/sigma.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/graphology-layout-forceatlas2@0.6.1/build/graphology-layout-forceatlas2.min.js"></script>
</head>
<body>
  <div id="sigma-container"></div>
  <script>
    // 随机生成节点和边
    const NODE_COUNT = 30000;
    const EDGE_COUNT = 60000;
    const graph = new graphology.Graph();

    // 添加节点
    for (let i = 0; i < NODE_COUNT; i++) {
      graph.addNode('n' + i, {
        label: 'Node ' + i,
        size: 6 + Math.random() * 6,
        color: '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0'),
        x: Math.random() * 100,
        y: Math.random() * 100
      });
    }

    // 添加边
    let edgeAdded = 0;
    while (edgeAdded < EDGE_COUNT) {
      let source = 'n' + Math.floor(Math.random() * NODE_COUNT);
      let target = 'n' + Math.floor(Math.random() * NODE_COUNT);
      if (source !== target && !graph.hasEdge(source, target)) {
        graph.addEdge(source, target);
        edgeAdded++;
      }
    }
 const forceAtlas2 = window['graphologyLayoutForceatlas2'];
    // 使用 ForceAtlas2 进行力导向布局
    forceAtlas2.assign(graph, { iterations: 200, settings: { gravity: 1, scalingRatio: 10 } });

    // 渲染
    const container = document.getElementById('sigma-container');
    const renderer = new Sigma(graph, container);
  </script>
</body>
</html>