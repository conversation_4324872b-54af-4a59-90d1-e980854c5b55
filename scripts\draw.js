const canvas = document.getElementById('bgParticles');
const ctx = canvas.getContext('2d');
let width = window.innerWidth;
let height = window.innerHeight;
// 处理高清屏
const dpr = window.devicePixelRatio || 1;

//   canvas.width = width;
//   canvas.height = height;

//   const particles = Array.from({ length: 80 }, () => ({
//     x: Math.random() * width,
//     y: Math.random() * height,
//     radius: Math.random() * 1.5 + 0.5,
//     speedY: Math.random() * 0.3 + 0.1,
//   }));

//   function draw() {
//     ctx.clearRect(0, 0, width, height);
//     ctx.fillStyle = "rgba(0,255,255,0.6)";
//     particles.forEach(p => {
//       ctx.beginPath();
//       ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
//       ctx.fill();
//       p.y += p.speedY;
//       if (p.y > height) p.y = 0;
//     });
//     requestAnimationFrame(draw);
//   }
// draw()

canvas.width = width * dpr;
canvas.height = height * dpr;
canvas.style.width = width + 'px';
canvas.style.height = height + 'px';
ctx.scale(dpr, dpr);

// 初始化粒子
const particles = Array.from({ length: 80 }, () => ({
  x: Math.random() * width,
  y: Math.random() * height,
  radius: Math.random() * 1.5 + 0.5,
  speedY: Math.random() * 0.3 + 0.1,
}));

function draw() {
  ctx.clearRect(0, 0, width, height);
  ctx.fillStyle = 'rgba(0,255,255,0.8)';
  particles.forEach((p) => {
    ctx.beginPath();
    ctx.arc(p.x, p.y, p.radius, 0, Math.PI * 2);
    ctx.fill();
    p.y += p.speedY;
    if (p.y > height) p.y = 0;
  });
  requestAnimationFrame(draw);
}

draw();
