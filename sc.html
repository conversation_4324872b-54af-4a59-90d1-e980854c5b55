<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>Tailwind 聚焦边框</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          animation: {
            pulseGlow: 'pulseGlow 1.2s infinite',
          },
          keyframes: {
            pulseGlow: {
              '0%, 100%': { transform: 'scale(1)', opacity: '1' },
              '50%': { transform: 'scale(1.4)', opacity: '0.5' },
            }
          }
        }
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .corner-line {
        @apply absolute w-4 h-4 border-2 border-cyan-400;
      }
      .corner-dot::after {
        content: '';
        @apply absolute w-[6px] h-[6px] bg-cyan-400 rounded-full top-[-4px] left-[-4px] animate-pulseGlow shadow-md;
      }
    }
  </style>
</head>
<body class="bg-[#050b20] min-h-screen flex items-center justify-center">

  <div class="relative group bg-cyan-500/5 backdrop-blur-sm w-[300px] p-6 rounded-md transition-shadow hover:shadow-[0_0_18px_#00ffff44] text-white">
    <!-- 聚焦角标 -->
    <div class="corner-line top-0 left-0 border-r-0 border-b-0 opacity-0 group-hover:opacity-100 transition-all corner-dot"></div>
    <div class="corner-line top-0 right-0 border-l-0 border-b-0 opacity-0 group-hover:opacity-100 transition-all corner-dot"></div>
    <div class="corner-line bottom-0 left-0 border-r-0 border-t-0 opacity-0 group-hover:opacity-100 transition-all corner-dot"></div>
    <div class="corner-line bottom-0 right-0 border-l-0 border-t-0 opacity-0 group-hover:opacity-100 transition-all corner-dot"></div>

    <!-- 内容 -->
    <h2 class="text-lg font-bold mb-2">布局配置</h2>
    <p class="text-sm text-white/70">悬浮时显示科技感边框，四角带动效</p>
  </div>

</body>
</html>
