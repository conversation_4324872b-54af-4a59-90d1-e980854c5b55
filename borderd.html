<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单边框带角块效果</title>
    <style>
        body {
            background-color: #1e1e1e; /* 更深的背景色 */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }

        .box-with-corners {
            position: relative; /* 相对定位，以便角块可以相对于它定位 */
            padding: 15px 25px; /* 内边距 */
            background-color: #252526; /* 盒子背景色 */
            color: #4fc1ff; /* 主要文本颜色 (蓝色) */
            font-family: 'Consolas', 'Monaco', monospace; /* 等宽字体 */
            font-size: 16px; /* 根据图片调整字体大小 */
            line-height: 1.6; /* 根据图片调整行高 */

            /* 单一边框 */
            border: 1px solid #6c6c6c; /* 使用较亮的灰色作为边框 */

            /* 预留空间，防止角块被遮挡 */
            margin: 10px;

            /* 示例宽度 */
            width: 280px; /* 根据图片调整宽度 */
        }

        /* (Lsp) 文本样式 */
        .box-with-corners .lsp {
            color: #808080;
            margin-left: 5px;
        }

        .corner {
            position: absolute; /* 绝对定位 */
            width: 3px; /* 角块宽度 */
            height: 3px; /* 角块高度 */
            background-color: #6c6c6c; /* 角块颜色 (与边框相同) */
            /* z-index: 1; */ /* 通常不需要，但如果遇到层叠问题可以加上 */
        }

        /* * 定位四个角块:
         * top/left/right/bottom 的 -2px 是这样计算的:
         * 边框厚度是 1px。角块是 3x3px。
         * 为了让角块的中心点与边框的角点对齐，需要移动 -(3px / 2) = -1.5px。
         * 我们还需要考虑 1px 的边框本身。
         * 将角块放在边框的外角，并使其覆盖边框的角点，-2px 是一个很好的视觉近似值。
         * 它使得 3x3 的方块有 1px 在边框外，1px 覆盖边框，1px 在边框内。
         */
        .tl { /* Top-Left */
            top: -2px;
            left: -2px;
        }

        .tr { /* Top-Right */
            top: -2px;
            right: -2px;
        }

        .bl { /* Bottom-Left */
            bottom: -2px;
            left: -2px;
        }

        .br { /* Bottom-Right */
            bottom: -2px;
            right: -2px;
        }
    </style>
</head>
<body>

    <div class="box-with-corners">
        <i class="corner tl"></i>
        <i class="corner tr"></i>
        <i class="corner bl"></i>
        <i class="corner br"></i>

        <div>
            console <span class="lsp">(Lsp)</span>
        </div>
        <div>
            crossOriginIsolated <span class="lsp">(Lsp)</span>
        </div>
    </div>

</body>
</html>
