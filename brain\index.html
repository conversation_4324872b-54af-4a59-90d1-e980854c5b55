<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Three.js 大脑图谱</title>
  <style>
    body { margin: 0; overflow: hidden; }
    #3d-graph { width: 100vw; height: 100vh; }
  </style>
</head>
<body>
  <div id="3d-graph"></div>

  <script src="https://cdn.jsdelivr.net/npm/3d-force-graph@1.71.1/dist/3d-force-graph.min.js"></script>
  <!-- ✅ 引入 Three.js -->
  <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>

  <script >

    const brainData = {
      nodes: [
        { id: '大脑皮层', group: 1, x: 0, y: 30, z: 0 },
        { id: '前额叶', group: 1, x: 20, y: 30, z: 10 },
        { id: '视觉皮层', group: 2, x: -30, y: 30, z: -10 },
        { id: '听觉皮层', group: 2, x: -10, y: 10, z: 20 },
        { id: '运动皮层', group: 3, x: 10, y: 10, z: 20 },
        { id: '躯体感觉皮层', group: 3, x: 30, y: 10, z: -10 },
        { id: '小脑', group: 4, x: 0, y: -30, z: 0 },
        { id: '脑干', group: 4, x: 0, y: -40, z: 10 },
        { id: '边缘系统', group: 5, x: 0, y: 0, z: 30 },
        { id: '海马体', group: 5, x: -10, y: 0, z: 40 },
        { id: '丘脑', group: 5, x: 10, y: 0, z: 40 },
        { id: '下丘脑', group: 5, x: 0, y: -10, z: 50 }
      ],
      links: [
        { source: '大脑皮层', target: '前额叶' },
        { source: '大脑皮层', target: '视觉皮层' },
        { source: '大脑皮层', target: '听觉皮层' },
        { source: '大脑皮层', target: '运动皮层' },
        { source: '大脑皮层', target: '躯体感觉皮层' },
        { source: '小脑', target: '脑干' },
        { source: '前额叶', target: '边缘系统' },
        { source: '边缘系统', target: '海马体' },
        { source: '边缘系统', target: '丘脑' },
        { source: '下丘脑', target: '丘脑' },
        { source: '海马体', target: '视觉皮层' }
      ]
    };

    const Graph = ForceGraph3D()(document.getElementById('3d-graph'))
      .graphData(brainData)
      .nodeLabel(node => node.id)
      .nodeAutoColorBy('group')
      .linkOpacity(0.5)
      .linkDirectionalParticles(2)
      .linkDirectionalParticleSpeed(0.01)
      .nodeThreeObjectExtend(true)
      .nodeThreeObject(node => {
        const sprite = document.createElement('canvas');
        const size = 128;
        sprite.width = size;
        sprite.height = size;
        const ctx = sprite.getContext('2d');

        // 渐变色，根据 group 变化
        const colorMap = {
          1: ['rgba(66,135,245,0.8)', 'rgba(66,135,245,0.2)'],
          2: ['rgba(245,66,179,0.8)', 'rgba(245,66,179,0.2)'],
          3: ['rgba(66,245,161,0.8)', 'rgba(66,245,161,0.2)'],
          4: ['rgba(245,183,66,0.8)', 'rgba(245,183,66,0.2)'],
          5: ['rgba(163,66,245,0.8)', 'rgba(163,66,245,0.2)']
        };
        const [colorStart, colorEnd] = colorMap[node.group] || ['rgba(255,255,255,0.8)', 'rgba(255,255,255,0.2)'];

        // 创建径向渐变
        const gradient = ctx.createRadialGradient(
          size/2, size/2, size/8,
          size/2, size/2, size/2 - 5
        );
        gradient.addColorStop(0, colorStart);
        gradient.addColorStop(1, colorEnd);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(size/2, size/2, size/2 - 5, 0, 2 * Math.PI);
        ctx.fill();

        const texture = new THREE.CanvasTexture(sprite);
        const material = new THREE.SpriteMaterial({ map: texture, transparent: true });
        const spriteObj = new THREE.Sprite(material);
        spriteObj.scale.set(6, 6, 1);
        return spriteObj;
      })
      .d3Force('center', null); // 取消自动居中
  </script>
</body>
</html>
