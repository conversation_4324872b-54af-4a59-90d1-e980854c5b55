# 3D Force Graph Project

This project demonstrates the use of a 3D force graph using the 3D Force Graph library. It includes two HTML files that showcase different methods of generating graph data.

## Files

### `graph.html`
This file contains a 3D force graph that generates nodes and links randomly. It utilizes scripts to create and display the graph, allowing for dynamic visualization of the data.

### `graph-manual.html`
This file manually generates data for the 3D force graph instead of using random values. It defines specific nodes and links with predetermined values, providing a controlled environment for testing and demonstration purposes.

## Usage

1. Open `graph.html` in a web browser to view the randomly generated 3D force graph.
2. Open `graph-manual.html` in a web browser to view the manually defined 3D force graph.

## Requirements
- A modern web browser that supports WebGL.
- Internet connection to load the 3D Force Graph library from the CDN.

## License
This project is open source and available for modification and distribution.