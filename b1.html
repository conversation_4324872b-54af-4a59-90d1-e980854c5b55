<!-- l border -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>带“L”形角的按钮</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* 定义 L 形的 clip-path */
    /* 这是一个基本的 L 形，短臂在左边，长臂向上 */

    /* 另一个 L 形定义，用于不同方向的旋转 */
    .l-shape-rotated-90 {
        clip-path: polygon(
            0% 0%,
            100% 0%,
            100% 20%,
            20% 20%,
            20% 100%,
            0% 100%
        );
    }
  </style>
</head>
<body class="flex items-center justify-center min-h-screen bg-gray-100">

  <button class="relative bg-lue-500 hover:bg-lue-600 text-white font-bold py-4 px-8 roundedlg shadow-lg overflow-hidden group w-64 h-24">
    <span class="relative z-10 text-xl">点击我</span>

    <div class="absolute top-0 left-0 w-8 h-8 bg-gray-300 transform -translate-x-1 -translate-y-1 l-shape-rotated-90 group-hover:bg-yellow-400 transition-colors duration-300"></div>

    <div class="absolute top-0 right-0 w-8 h-8 bg-gray-300 transform translate-x-1 -translate-y-1 rotate-90 l-shape-rotated-90 group-hover:bg-yellow-400 transition-colors duration-300"></div>

    <div class="absolute bottom-0 left-0 w-8 h-8 bg-gray-300 transform -translate-x-1 translate-y-1 -rotate-90 l-shape-rotated-90 group-hover:bg-yellow-400 transition-colors duration-300"></div>

    <div class="absolute bottom-0 right-0 w-8 h-8 bg-gray-300 transform translate-x-1 translate-y-1 rotate-180 l-shape-rotated-90 group-hover:bg-yellow-400 transition-colors duration-300"></div>
  </button>

</body>
</html>