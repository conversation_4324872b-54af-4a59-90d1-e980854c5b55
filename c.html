<head>
  <style> body { margin: 0; } </style>
  <script src="//cdn.jsdelivr.net/npm/3d-force-graph"></script>
</head>
<body>
<div id="3d-graph"></div>
<script>
  const data = {
    "nodes": [
      {"id": "A", "group": 1},
      {"id": "B", "group": 1},
      {"id": "C", "group": 2},
      {"id": "D", "group": 2},
      {"id": "E", "group": 3},
      {"id": "F", "group": 3},
      {"id": "win", "group": 3},
      {"id": "pc", "group": 3},
      {"id": "mac", "group": 3},
      {"id": "ubn", "group": 3}
    ],
    "links": [
      {"source": "A", "target": "B", "value": 1},
      {"source": "A", "target": "C", "value": 2},
      {"source": "B", "target": "D", "value": 2},
      {"source": "C", "target": "E", "value": 1},
      {"source": "D", "target": "F", "value": 1},
      {"source": "E", "target": "F", "value": 3},
      {"source": "ubn", "target": "pc",},
      {"source": "mac", "target": "pc", },
      {"source": "win", "target": "pc", },
    ]
  };

  const Graph = new ForceGraph3D(document.getElementById('3d-graph'))
    .graphData(data)
    .nodeLabel('id')
    .cooldownTicks(8)
    .nodeAutoColorBy('group');
</script>
</body>