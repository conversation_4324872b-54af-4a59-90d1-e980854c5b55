<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动漫科技信息面板 - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    // 添加自定义辉光效果（可选，但推荐）
                    boxShadow: {
                        'glow-cyan': '0 0 15px theme(colors.cyan.500)',
                        'glow-green': '0 0 10px theme(colors.green.500)',
                        'glow-red': '0 0 10px theme(colors.red.500)',
                    },
                    // 添加动画
                    keyframes: {
                        flicker: {
                            '0%, 100%': { opacity: 1 },
                            '50%': { opacity: 0.7 }, // Your existing flicker
                        },
                        // Optional: a more subtle flicker for borders if desired
                        borderFlicker: {
                            '0%, 100%': { opacity: 0.8 },
                            '50%': { opacity: 0.6 },
                            '25%, 75%': { opacity: 0.75 },
                        }
                    },
                    animation: {
                        flicker: 'flicker 1.5s infinite',
                        borderFlicker: 'borderFlicker 2s infinite', // Slower, more subtle flicker
                    }
                }
            }
        }
    </script>
    <style>
        /* (可选) 添加一个简单的背景网格效果 */
        body {
            background-color: #0a0c10;
            background-image:
                linear-gradient(rgba(0, 180, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0, 180, 255, 0.05) 1px, transparent 1px);
            background-size: 30px 30px;
        }
        /* 隐藏滚动条 */
        ::-webkit-scrollbar {
            display: none;
        }
    </style>
</head>
<body class="flex justify-center items-center min-h-screen font-mono p-4">

    <div class="
        relative                      w-full max-w-md                      bg-slate-900/70                      border-2 border-cyan-500/60    p-6                                  rounded-lg                     shadow-2xl shadow-cyan-500/30 backdrop-blur-sm                      text-cyan-200                         overflow-hidden                      ">

        <div class="absolute top-1 left-1 w-6 h-6 border-t-2 border-l-2 border-cyan-400 opacity-80 rounded-tl-md **animate-borderFlicker**"></div>
        <div class="absolute top-1 right-1 w-6 h-6 border-t-2 border-r-2 border-cyan-400 opacity-80 rounded-tr-md **animate-borderFlicker**"></div>
        <div class="absolute bottom-1 left-1 w-6 h-6 border-b-2 border-l-2 border-cyan-400 opacity-80 rounded-bl-md **animate-borderFlicker**"></div>
        <div class="absolute bottom-1 right-1 w-6 h-6 border-b-2 border-r-2 border-cyan-400 opacity-80 rounded-br-md **animate-borderFlicker**"></div>

        <div class="absolute top-4 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-500/50 to-transparent"></div>

        <div class="flex justify-between items-center border-b border-cyan-700/30 pb-3 mb-6 z-10 relative">
            <h1 class="text-2xl font-bold text-cyan-300 tracking-widest uppercase filter drop-shadow-[0_0_3px_theme(colors.cyan.400)]">
                // SYSTEM_CORE
            </h1>
            <div class="flex items-center space-x-2">
                   <span class="text-xs text-green-400">ONLINE</span>
                   <div class="w-3 h-3 bg-green-500 rounded-full shadow-glow-green animate-pulse"></div>
            </div>
        </div>

        <div class="space-y-5 z-10 relative">

            <div class="flex justify-between items-center text-sm opacity-90 hover:opacity-100 transition duration-200">
                <span class="text-slate-400 w-1/3">// MECH_ID:</span>
                <span class="text-orange-400 font-bold tracking-wider">RX-0_UNICORN</span>
            </div>

            <div class="flex justify-between items-center text-sm opacity-90 hover:opacity-100 transition duration-200">
                <span class="text-slate-400 w-1/3">// PILOT_SIG:</span>
                <span class="text-purple-400 font-bold tracking-wider">[_CLASSIFIED_]</span>
            </div>

            <div class="text-sm">
                <span class="text-slate-400 block mb-2">// POWER_LEVEL:</span>
                <div class="w-full bg-gray-700/60 rounded-sm h-3 border border-cyan-900/50 overflow-hidden">
                    <div class="bg-green-500 h-full w-[85%] shadow-glow-green transition-all duration-500 flex items-center justify-end pr-1">
                       <span class="text-xs text-slate-900 font-bold">85%</span>
                    </div>
                </div>
            </div>

            <div class="text-sm">
                <span class="text-slate-400 block mb-2">// SUB_SYSTEMS:</span>
                <ul class="list-none pl-4 space-y-2 border-l border-cyan-800/40 ml-2">
                    <li class="flex items-center justify-between hover:bg-slate-800/50 p-1 rounded-sm">
                        <span><span class="text-cyan-500 mr-2">></span>WEAPONS</span>
                        <span class="text-green-400 bg-green-900/50 px-2 py-0.5 text-xs rounded-sm">ACTIVE</span>
                    </li>
                    <li class="flex items-center justify-between hover:bg-slate-800/50 p-1 rounded-sm">
                        <span><span class="text-cyan-500 mr-2">></span>SHIELDS</span>
                        <span class="text-green-400 bg-green-900/50 px-2 py-0.5 text-xs rounded-sm">ACTIVE</span>
                    </li>
                    <li class="flex items-center justify-between hover:bg-slate-800/50 p-1 rounded-sm">
                        <span><span class="text-cyan-500 mr-2">></span>SENSORS</span>
                        <span class="text-yellow-400 bg-yellow-900/50 px-2 py-0.5 text-xs rounded-sm">STANDBY</span>
                    </li>
                    <li class="flex items-center justify-between hover:bg-slate-800/50 p-1 rounded-sm">
                        <span><span class="text-cyan-500 mr-2">></span>LIFE_SUPPORT</span>
                        <span class="text-red-400 bg-red-900/50 px-2 py-0.5 text-xs rounded-sm animate-flicker">WARNING</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="border-t border-cyan-700/30 pt-4 mt-8 flex justify-end z-10 relative">
               <span class="text-xs text-slate-500">:: AETHER_OS_v4.7.2 ::</span>
        </div>

    </div>

</body>
</html>