<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部分边框发光的不规则按钮</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #0b1120; /* 深色背景 */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; /* 提升字体美观 */
        }

        .partial-glow-button {
            background-color: #1a202c; /* 按钮深色背景 */
            color: #d1d5db; /* 浅色文字 */
            padding: 1rem 2.5rem; /* 增加左右内边距 */
            font-size: 1.25rem; /* 稍微大一点的文字 */
            font-weight: 600; /* 半粗体 */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative; /* 为伪元素定位提供上下文 */
            overflow: hidden; /* 确保伪元素的发光不会超出按钮的视觉边界 */
            transition: all 0.3s ease; /* 基础过渡效果 */

            /* 模拟不规则的圆角，使按钮看起来更独特 */
            border-top-left-radius: 1.5rem;
            border-bottom-right-radius: 1.5rem;
            border-top-right-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;

            /* 按钮本身的边框，可以根据需要调整颜色和透明度 */
            border: 1px solid rgba(255, 223, 0, 0.2); /* 按钮自身的淡黄色边框 */

            /* 伪元素用于发光效果 */
            &::before,
            &::after {
                content: '';
                position: absolute;
                /* 使用 z-index 确保伪元素在按钮下方，这样发光效果在按钮内容后面 */
                z-index: 0;
                filter: blur(10px); /* 模糊效果，创建光晕 */
                opacity: 0.7; /* 调整光晕的透明度 */
                transition: opacity 0.3s ease, transform 0.3s ease; /* 悬停过渡 */
            }

            /* 顶部发光 */
            &::before {
                background: linear-gradient(90deg, rgba(255,223,0,0) 0%, rgba(255,223,0,0.8) 25%, rgba(255,223,0,0.8) 75%, rgba(255,223,0,0) 100%);
                top: -5px; /* 稍微超出顶部 */
                left: 10%; /* 稍微偏右开始 */
                right: 10%; /* 稍微偏左结束 */
                height: 15px; /* 光晕的高度 */
                border-radius: 9999px; /* 使顶部发光看起来更柔和圆滑 */
            }

            /* 底部发光 */
            &::after {
                background: linear-gradient(90deg, rgba(255,223,0,0) 0%, rgba(255,223,0,0.8) 25%, rgba(255,223,0,0.8) 75%, rgba(255,223,0,0) 100%);
                bottom: -5px; /* 稍微超出底部 */
                left: 10%; /* 稍微偏右开始 */
                right: 10%; /* 稍微偏左结束 */
                height: 15px; /* 光晕的高度 */
                border-radius: 9999px; /* 使底部发光看起来更柔和圆滑 */
            }

            /* 悬停效果 */
            &:hover {
                transform: translateY(-3px); /* 轻微上浮 */
                border-color: rgba(255, 223, 0, 0.6); /* 悬停时边框稍微亮一点 */

                &::before,
                &::after {
                    opacity: 1; /* 悬停时光晕更亮 */
                    transform: scaleX(1.1); /* 悬停时光晕稍微变宽 */
                }
            }

            /* 确保按钮文字在伪元素之上 */
            & > span {
                position: relative;
                z-index: 1;
            }
        }
    </style>
</head>
<body>

    <button class="partial-glow-button">
        <span>年夜饭</span>
    </button>

</body>
</html>