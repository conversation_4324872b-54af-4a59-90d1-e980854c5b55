<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>3D Force Graph Example</title>
    <style>
      body { margin: 0; overflow: hidden; }
      canvas { display: block; }
    </style>
  </head>
  <body>
    <script src="https://unpkg.com/3d-force-graph"></script>
    <script>
      const data = {
        nodes: [
          { id: 'A', group: 1 },
          { id: 'B', group: 1 },
          { id: 'C', group: 2 },
          { id: 'D', group: 2 },
          { id: 'E', group: 3 }
        ],
        links: [
          { source: 'A', target: 'B' },
          { source: 'A', target: 'C' },
          { source: 'C', target: 'D' },
          { source: 'D', target: 'E' }
        ]
      }

      const Graph = ForceGraph3D()(document.body)
        .graphData(data)
        .nodeAutoColorBy('group')  // 根据 group 字段自动着色
        .nodeLabel(node => `ID: ${node.id}, Group: ${node.group}`)
        .nodeOpacity(0.85)
        .linkOpacity(0.3)
    </script>
  </body>
</html>
