<!DOCTYPE html>
<html lang="en" class="bg-gray-900 text-gray-100">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Industrial Dashboard</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="font-mono">
  <!-- Top nav -->
  <header class="bg-gray-800 shadow-md p-4 flex justify-between items-center border-b border-gray-700">
    <h1 class="text-xl font-bold tracking-widest">INDUSTRIAL UI</h1>
    <nav>
      <button class="bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded">Login</button>
    </nav>
  </header>

  <!-- Sidebar + Main -->
  <div class="flex h-screen">
    <!-- Sidebar -->
    <aside class="w-64 bg-gray-850 border-r border-gray-700 p-4">
      <ul class="space-y-4">
        <li><a href="#" class="text-gray-300 hover:text-white">Dashboard</a></li>
        <li><a href="#" class="text-gray-300 hover:text-white">Machines</a></li>
        <li><a href="#" class="text-gray-300 hover:text-white">Reports</a></li>
        <li><a href="#" class="text-gray-300 hover:text-white">Settings</a></li>
      </ul>
    </aside>

    <!-- Main content -->
    <main class="flex-1 p-6 bg-gray-900">
      <h2 class="text-2xl font-bold mb-6 border-b pb-2 border-gray-700">Dashboard Overview</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Card 1 -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
          <h3 class="font-semibold mb-2">System Status</h3>
          <p class="text-sm text-gray-400">All systems operational</p>
        </div>
        <!-- Card 2 -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-4 border border-gray-700">
          <h3 class="font-semibold mb-2">Machine Uptime</h3>
          <canvas id="uptimeChart" height="150"></canvas>
        </div>
      </div>
    </main>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="main.js"></script>
</body>
</html>
