<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>单边框带角块效果 - Tailwind CSS</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      // 你可以在这里配置 Tailwind，但对于这个例子，默认配置加上任意值就足够了
      tailwind.config = {
        theme: {
          extend: {
            // 如果需要，可以添加自定义颜色、尺寸等
          },
        },
      };
    </script>
  </head>
  <body class="bg-[#1e1e1e] flex justify-center items-center min-h-screen m-0">
    <div
      class="relative px-[25px] py-[15px] bg-[#252526] text-[#4fc1ff] font-mono text-base leading-[1.6] border border-[#6c6c6c] m-[10px] w-[280px]">
      <i
        class="absolute w-[3px] h-[3px] bg-[#6c6c6c] top-[-2px] left-[-2px]"></i>
      <i
        class="absolute w-[3px] h-[3px] bg-[#6c6c6c] top-[-2px] right-[-2px]"></i>
      <i
        class="absolute w-[3px] h-[3px] bg-[#6c6c6c] bottom-[-2px] left-[-2px]"></i>
      <i
        class="absolute w-[3px] h-[3px] bg-[#6c6c6c] bottom-[-2px] right-[-2px]"></i>
      <div>
        crossOriginIsolated
        <span class="text-[#808080] ml-[5px]">(Lsp)</span>
      </div>
    </div>
  </body>
</html>
