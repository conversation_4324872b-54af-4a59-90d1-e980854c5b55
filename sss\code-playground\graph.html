<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<title>3D Force Graph Manual Data Example</title>
	<style>
		html,
		body {
			margin: 0;
			padding: 0;
			overflow: hidden;
			width: 100%;
			height: 100%;
		}

		#3d-graph {
			width: 100%;
			height: 100%;
		}
	</style>
</head>

<body>
	<div id="3d-graph"></div>

	<!-- 引入依赖 -->
	<script src="https://unpkg.com/three@0.152.2/build/three.min.js"></script>
	<script src="https://unpkg.com/3d-force-graph"></script>

	<script>
		const nodes = [
			{ id: '0', x: 100, y: 200, z: 300 },
			{ id: '1', x: 200, y: 300, z: 400 },
			{ id: '2', x: 300, y: 400, z: 500 },
			{ id: '3', x: 400, y: 500, z: 600 },
			{ id: '4', x: 500, y: 600, z: 700 }
		];

		const links = [
			{ source: '0', target: '1' },
			{ source: '1', target: '2' },
			{ source: '2', target: '3' },
			{ source: '3', target: '4' },
			{ source: '4', target: '0' }
		];

		const myGraph = ForceGraph3D()(document.getElementById('3d-graph'))
			.graphData({ nodes, links })
			.nodeLabel(node => node.id)
			.nodeAutoColorBy('id')
			.nodeRelSize(6)
			.enableNodeDrag(false)
			.cooldownTicks(0);

		let lastHoverNode = null;
		myGraph.onNodeHover(node => {
			if (lastHoverNode) {
				lastHoverNode.__val = 6;
			}
			if (node) {
				node.__val = 18;
			}
			lastHoverNode = node;
			myGraph.nodeVal(n => n.__val || 6);
		});

		nodes.forEach(n => n.__val = 6);
		myGraph.nodeVal(n => n.__val);

		myGraph.onNodeClick(node => {
			alert(`节点ID: ${node.id}`);
		});
	</script>
</body>

</html>